from pwn import *

HOST = '*************'
PORT = 12343

# large enough mod
mod = str(10**60 + 1337)
guess = '1'  # based on <PERSON><PERSON>'s Law

r = remote(HOST, PORT)

for stage in range(100):
    r.recvuntil(b'Stage')
    print(f'[+] Stage {stage+1}')

    for _ in range(2000):
        r.recvuntil(b'Enter mod:')
        r.sendline(mod.encode())

    r.recvuntil(b'Which number (1~9) appeared the most? :')
    r.sendline(guess.encode())

output = r.recvall().decode()
print(output)
